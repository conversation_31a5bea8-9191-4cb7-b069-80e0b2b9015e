import sanitizeHtml from 'sanitize-html';

/**
 * Utilities for cleaning and transforming Confluence content
 */

/**
 * Extracts plain text from Confluence Storage Format (XHTML)
 * This is a simple implementation - a production version would use a proper HTML parser
 */
export function extractTextFromStorage(storageFormat: string): string {
  if (!storageFormat) return '';

  // Simple regex to strip HTML tags
  return storageFormat
    .replace(/<[^>]*>/g, ' ') // Replace HTML tags with spaces
    .replace(/&[a-z]+;/g, ' ') // Replace HTML entities
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Truncates content to a specified length, preserving word boundaries
 */
export function truncateContent(content: string, maxLength: number = 8000): string {
  if (content.length <= maxLength) return content;

  // Find a good breaking point
  const breakPoint = content.lastIndexOf(' ', maxLength);
  if (breakPoint === -1) return content.substring(0, maxLength);

  return content.substring(0, breakPoint) + '...';
}

/**
 * Optimizes content for AI context windows by removing redundant information
 * and focusing on the most important parts
 */
export function optimizeForAI(content: string): string {
  // This is a placeholder - a real implementation would be more sophisticated
  return truncateContent(content);
}

/**
 * Converts Confluence Storage Format to Markdown
 * This is a simple implementation - a production version would be more comprehensive
 */
export function storageFormatToMarkdown(storageFormat: string): string {
  if (!storageFormat) return '';

  let markdown = storageFormat;

  // 保留代码块 - 处理 ac:structured-macro name="code"
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="code"[^>]*>[\s\S]*?<ac:parameter[^>]*ac:name="language"[^>]*>([^<]*)<\/ac:parameter>[\s\S]*?<ac:plain-text-body><!\[CDATA\[([\s\S]*?)\]\]><\/ac:plain-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    '```$1\n$2\n```'
  );

  // 处理没有语言参数的代码块
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="code"[^>]*>[\s\S]*?<ac:plain-text-body><!\[CDATA\[([\s\S]*?)\]\]><\/ac:plain-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    '```\n$1\n```'
  );

  // 保留图片信息 - 转换为Markdown图片格式
  markdown = markdown.replace(
    /<ac:image[^>]*>[\s\S]*?<ri:attachment[^>]*ri:filename="([^"]+)"[^>]*\/>[\s\S]*?<\/ac:image>/g,
    '![图片]($1 "Confluence附件: $1")'
  );

  // 处理普通img标签
  markdown = markdown.replace(
    /<img[^>]*src="([^"]+)"[^>]*alt="([^"]*)"[^>]*>/g,
    '![$2]($1)'
  );
  markdown = markdown.replace(
    /<img[^>]*src="([^"]+)"[^>]*>/g,
    '![图片]($1)'
  );

  // 保留信息宏 - info, note, warning, tip
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="(info|note|warning|tip)"[^>]*>[\s\S]*?<ac:rich-text-body>([\s\S]*?)<\/ac:rich-text-body>[\s\S]*?<\/ac:structured-macro>/g,
    (match, type, content) => {
      const icon = { info: 'ℹ️', note: '📝', warning: '⚠️', tip: '💡' }[type] || '📌';
      return `\n> ${icon} **${type.toUpperCase()}**\n> ${content.replace(/\n/g, '\n> ')}\n`;
    }
  );

  // 保留表格
  markdown = markdown.replace(/<table[^>]*>([\s\S]*?)<\/table>/g, (match, tableContent) => {
    // 简单的表格转换 - 可以进一步优化
    let table = tableContent;
    table = table.replace(/<tr[^>]*>([\s\S]*?)<\/tr>/g, '|$1|\n');
    table = table.replace(/<t[hd][^>]*>([\s\S]*?)<\/t[hd]>/g, ' $1 |');
    table = table.replace(/\|\s*\|/g, '|');
    return '\n' + table + '\n';
  });

  // Convert headings
  markdown = markdown.replace(/<h1[^>]*>(.*?)<\/h1>/g, '# $1');
  markdown = markdown.replace(/<h2[^>]*>(.*?)<\/h2>/g, '## $1');
  markdown = markdown.replace(/<h3[^>]*>(.*?)<\/h3>/g, '### $1');
  markdown = markdown.replace(/<h4[^>]*>(.*?)<\/h4>/g, '#### $1');
  markdown = markdown.replace(/<h5[^>]*>(.*?)<\/h5>/g, '##### $1');
  markdown = markdown.replace(/<h6[^>]*>(.*?)<\/h6>/g, '###### $1');

  // Convert paragraphs
  markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/g, '$1\n\n');

  // Convert bold
  markdown = markdown.replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**');
  markdown = markdown.replace(/<b[^>]*>(.*?)<\/b>/g, '**$1**');

  // Convert italic
  markdown = markdown.replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*');
  markdown = markdown.replace(/<i[^>]*>(.*?)<\/i>/g, '*$1*');

  // Convert links
  markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/g, '[$2]($1)');

  // Convert lists
  markdown = markdown.replace(/<ul[^>]*>(.*?)<\/ul>/g, '$1\n');
  markdown = markdown.replace(/<ol[^>]*>(.*?)<\/ol>/g, '$1\n');
  markdown = markdown.replace(/<li[^>]*>(.*?)<\/li>/g, '- $1\n');

  // 保留其他重要宏的信息
  markdown = markdown.replace(
    /<ac:structured-macro[^>]*ac:name="([^"]+)"[^>]*>[\s\S]*?<\/ac:structured-macro>/g,
    '\n[Confluence宏: $1]\n'
  );

  // Remove remaining HTML tags
  markdown = sanitizeHtml(markdown, { allowedTags: [], allowedAttributes: {} });

  // Fix spacing
  markdown = markdown.replace(/\n\s*\n/g, '\n\n');

  return markdown.trim();
}

/**
 * 提取 Confluence 存储格式内容中的图片引用（ac:image、img标签等）
 * 返回图片的文件名、src等信息，便于AI识别页面图片
 */
export function extractImageAttachments(xhtml: string): { filename?: string, src?: string }[] {
  const images: { filename?: string, src?: string }[] = [];
  if (!xhtml) return images;
  // 匹配 <ac:image> 标签下的 <ri:attachment ri:filename="..." />
  const acImageRegex = /<ac:image[^>]*>[\s\S]*?<ri:attachment[^>]*ri:filename="([^"]+)"[^>]*\/>[\s\S]*?<\/ac:image>/g;
  let match;
  while ((match = acImageRegex.exec(xhtml)) !== null) {
    images.push({ filename: match[1] });
  }
  // 匹配 <img src="..."> 标签
  const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/g;
  while ((match = imgRegex.exec(xhtml)) !== null) {
    images.push({ src: match[1] });
  }
  return images;
}

